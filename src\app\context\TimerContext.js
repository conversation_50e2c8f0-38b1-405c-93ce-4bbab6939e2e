"use client";
import { createContext, useContext, useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import refreshTokenApi from "../api/onboarding/refreshTokenEndpoint";
import { customFetchWithToken } from "../utils/axiosInterpreter";
import axios from "axios";

const Base_url = process.env.NEXT_PUBLIC_Base_URL;
const TimerContext = createContext();

const MODAL_TRIGGER_TIME = 7 * 60; // 7 minutes in seconds
const LOGOUT_TIME = 8 * 60; // 8 minutes in seconds

const LAST_ACTIVITY_KEY = "last_activity_time";
const SESSION_REFRESH_KEY = "session_refresh_allowed";

export const TimerContextProvider = ({ children }) => {
  const router = useRouter();
  const [showModal, setShowModal] = useState(false);
  const [sessionRefreshAllowed, setSessionRefreshAllowed] = useState(true);
  const [lastActivity, setLastActivity] = useState(null);
  const [isLogout, setIsLogout] = useState(false);
  const inactivityTimerRef = useRef(null);

  // Initialize inactivity tracking
  useEffect(() => {
    const initializeInactivityTimer = () => {
      const userToken = sessionStorage.getItem("user");

      if (userToken) {
        const currentTime = Date.now();
        setLastActivity(currentTime);
        localStorage.setItem(LAST_ACTIVITY_KEY, currentTime.toString());
        startInactivityTimer();
      } else {
        resetTimerState();
      }
    };

    if (typeof window !== "undefined") {
      initializeInactivityTimer();
    }
  }, []);

  const startInactivityTimer = () => {
    if (inactivityTimerRef.current) {
      clearTimeout(inactivityTimerRef.current);
    }

    inactivityTimerRef.current = setTimeout(() => {
      setShowModal(true);

      // Set another timeout for final logout after 1 minute
      setTimeout(() => {
        if (!isLogout) {
          stopTimer();
        }
      }, 60 * 1000); // 1 minute after modal shows
    }, MODAL_TRIGGER_TIME * 1000);
  };

  const resetInactivityTimer = () => {
    const currentTime = Date.now();
    setLastActivity(currentTime);
    localStorage.setItem(LAST_ACTIVITY_KEY, currentTime.toString());
    setShowModal(false); // Hide modal if user becomes active
    startInactivityTimer();
  };

  // Activity event listeners
  useEffect(() => {
    const activityEvents = ['mousemove', 'keydown', 'click', 'scroll', 'touchstart'];

    const handleActivity = () => {
      if (!isLogout) {
        resetInactivityTimer();
      }
    };

    const userToken = sessionStorage.getItem("user");
    if (userToken && !isLogout) {
      activityEvents.forEach(event => {
        window.addEventListener(event, handleActivity, { passive: true });
      });
    }

    return () => {
      activityEvents.forEach(event => {
        window.removeEventListener(event, handleActivity);
      });
      if (inactivityTimerRef.current) {
        clearTimeout(inactivityTimerRef.current);
      }
    };
  }, [isLogout]);

  const startTimer = () => {
    const currentTime = Date.now();
    setLastActivity(currentTime);
    localStorage.setItem(LAST_ACTIVITY_KEY, currentTime.toString());
    localStorage.setItem(SESSION_REFRESH_KEY, "true");
    setShowModal(false); // Force modal closure on new timer
    setSessionRefreshAllowed(true);
    setIsLogout(false);
    startInactivityTimer();
  };

  const stopTimer = () => {
    toast.info("Session expired. Please log in again.");
    setIsLogout(true);

    setTimeout(() => {
      if (typeof window !== "undefined") {
        localStorage.clear();
        sessionStorage.clear();
      }
      if (typeof window !== "undefined") {
        window.location.href = "/sign/login";
      }
    }, 1500);
  };

  const handleSessionContinue = async () => {
    try {
      if (window !== "undefined") {
        let refreshToken = localStorage.getItem("refreshToken");
        const response = await axios({
          url: `${Base_url}/get-access-token/?refresh_token=${refreshToken}`,
          method: "GET",
        });
        sessionStorage.setItem("user", response.data.data.access_token);
        startTimer();
        toast.success("Session refreshed!");
      }
    } catch (error) {
      console.error(error);
      toast.error("Could not refresh session.");
    }
  };

  const resetTimerState = () => {
    setLastActivity(null);
    setShowModal(false);
    setSessionRefreshAllowed(false);
    if (inactivityTimerRef.current) {
      clearTimeout(inactivityTimerRef.current);
    }
  };

  return (
    <TimerContext.Provider
      value={{
        showModal,
        sessionRefreshAllowed,
        handleSessionContinue,
        handleSessionEnd: stopTimer,
        startTimer,
        stopTimer,
      }}
    >
      {children}
    </TimerContext.Provider>
  );
};

export const useTimer = () => {
  const context = useContext(TimerContext);
  if (!context) {
    throw new Error("useTimer must be used within TimerContextProvider");
  }
  return context;
};
